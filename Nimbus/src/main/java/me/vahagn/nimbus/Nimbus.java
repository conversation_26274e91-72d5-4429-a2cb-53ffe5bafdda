package me.vahagn.nimbus;

import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.event.Listener;
import org.bukkit.plugin.Plugin;
import org.bukkit.util.Vector;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.Ability;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.AirAbility;
import com.projectkorra.projectkorra.ability.CoreAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsMethods;

public class Nimbus extends AirAbility implements AddonAbility
{
    public static final String NAME = "Nimbus";
    private long cooldown;
    private double speed;
    private double duration;
    private boolean rain;
    long currentLevel;
    
    public Nimbus(final Player player) {
        super(player);
        if (this.bPlayer.canBend((CoreAbility)this)) {
            final int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
            this.currentLevel = TLBMethods.limitLevels(this.player, statLevel);
        
            this.speed = TLBMethods.getDouble("Abilities.Varhagna.Nimbus.Speed", currentLevel);
            this.cooldown = TLBMethods.getLong("Abilities.Varhagna.Nimbus.Cooldown", currentLevel);
            this.duration = TLBMethods.getDouble("Abilities.Varhagna.Nimbus.Duration", currentLevel);
            this.rain =  ConfigManager.getConfig().getBoolean("Abilities.Varhagna.Nimbus.RainEnabled");
            this.start();
        }
    }
    
    public long getCooldown() {
        return this.cooldown;
    }
    
    public Location getLocation() {
        return this.player.getLocation();
    }
    
    public String getName() {
        return "Nimbus";
    }
    
    public boolean isHarmlessAbility() {
        return false;
    }
    
    public boolean isSneakAbility() {
        return true;
    }
    
    public void progress() {
        final Vector velocity = this.player.getEyeLocation().getDirection().normalize().clone().multiply(this.speed);
        if (this.player.isSneaking()) {
            velocity.multiply(1.1);
            this.duration -= 30.0;
        }
        if (isWater(this.player.getLocation().getBlock().getType()) || isLava(this.player.getLocation().getBlock().getType())) {
            this.bPlayer.addCooldown((Ability)this);
            this.remove();
            return;
        }
        
        // Display particles only if this is the active instance
        if (CoreAbility.getAbility(this.player, Nimbus.class) == this) {
            ParticleEffect.CLOUD.display(this.player.getLocation().add(velocity), 10, 0.7, 0.3, 0.7, 0.0);
            ParticleEffect.CLOUD.display(GeneralMethods.getRightSide(this.player.getLocation(), 1.0), 4, 0.1, 0.2, 0.1, 0.0);
            ParticleEffect.CLOUD.display(GeneralMethods.getLeftSide(this.player.getLocation(), 1.0), 4, 0.1, 0.2, 0.1, 0.0);
            if (this.rain) {
                ParticleEffect.DRIP_WATER.display(this.player.getLocation().subtract(0.0, 1.0, 0.0).add(velocity), 22, 0.699, 0.300, 0.699, 0.0);
            }
        }
        
        this.player.setVelocity(velocity);
        if (this.getStartTime() + this.duration < System.currentTimeMillis()) {
            this.bPlayer.addCooldown((Ability)this);
            this.remove();
        }
    }
    
    public String getAuthor() {
        return "Varhagna";
    }
    
    public String getVersion() {
        return "1.3.0";
    }
    
    public String getDescription() {
        return "By understanding the Ancient Airbending techniques, you can create your very own cloud to ride wherever you want.";
    }
    
    public String getInstructions() {
        return "Sprint then left click to start riding a cloud in the direction you look. Sneaking will boost your speed at the cost of duration!";
    }
    
    public void load() {
        ProjectKorra.plugin.getServer().getPluginManager().registerEvents((Listener)new NimbusListener(), (Plugin)ProjectKorra.plugin);
        ConfigManager.defaultConfig.get().addDefault("Abilities.Varhagna.Nimbus.Cooldown", (Object)4500);
        ConfigManager.defaultConfig.get().addDefault("Abilities.Varhagna.Nimbus.Duration", (Object)3500);
        ConfigManager.defaultConfig.get().addDefault("Abilities.Varhagna.Nimbus.Speed", (Object)0.7);
        ConfigManager.defaultConfig.get().addDefault("Abilities.Varhagna.Nimbus.RainEnabled", (Object)true);
    }
    
    public void stop() {
        // Remove all active instances of Nimbus when the plugin is stopped/reloaded
        for (Player player : ProjectKorra.plugin.getServer().getOnlinePlayers()) {
            CoreAbility.getAbility(player, Nimbus.class).remove();
        }
    }
}
