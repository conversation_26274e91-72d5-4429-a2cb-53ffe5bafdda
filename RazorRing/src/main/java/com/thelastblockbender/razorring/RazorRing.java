package com.thelastblockbender.razorring;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.projectkorra.projectkorra.Element;
import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.CoreAbility;
import com.projectkorra.projectkorra.ability.WaterAbility;
import com.projectkorra.projectkorra.ability.util.Collision;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.earthbending.EarthSmash;
import com.projectkorra.projectkorra.firebending.WallOfFire;
import com.projectkorra.projectkorra.util.DamageHandler;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import com.projectkorra.projectkorra.util.TempBlock;
import me.moros.hyperion.abilities.firebending.Combustion;
import me.moros.hyperion.util.BendingFallingBlock;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.block.data.BlockData;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.util.BoundingBox;
import org.bukkit.util.Vector;

public class RazorRing extends WaterAbility implements AddonAbility {
  enum Stage {SOURCING, FORMING_RING, RING_READY, LAUNCHING}

  private static final double RING_RADIUS = 3;

  private long cooldown;
  private long iceDuration;
  private double sourceRadius;
  private double ringRadius;
  private double maxRingRadius;
  private double range;
  private double damage;
  private double speed;
  private double collisionRadius;
  private Location location;
  private Vector direction;
  private TempBlock source;
  private final Collection<Entity> affectedEntities = new HashSet<>();
  private final Collection<TempBlock> ringBlocks = new HashSet<>();

  private Stage stage = Stage.SOURCING;
  private boolean freeze = false;
  private boolean hit = false;
  private boolean cooperative = false;

  private double startAngle = 0;
  private double angle = 0;
  private double distanceTravelled = 0;

  public RazorRing(Player player) {
    super(player);

    if (!bPlayer.canBend(this) || hasAbility(player, RazorRing.class)) {
      return;
    }

    setFields();
    stage = Stage.SOURCING;
    
    // Check for cooperative mode with another waterbender
    for (Entity entity : GeneralMethods.getEntitiesAroundPoint(player.getEyeLocation(), sourceRadius)) {
      if (entity instanceof Player other && !player.equals(other)) {
        RazorRing ring = getAbility(other, RazorRing.class);
        if (ring != null && ring.stage == Stage.RING_READY) {
          cooperative = true;
          ring.cooperative = true;
          if (ring.launch(player)) {
            return;
          }
        }
      }
    }

    if (prepare()) {
      start();
    }
  }

  public void setFields() {
    int statLevel = StatisticsMethods.getId("AbilityLevel_" + getName());
    long currentLevel = TLBMethods.limitLevels(player, statLevel);

    cooldown = TLBMethods.getLong("ExtraAbilities.Moros.RazorRing.Cooldown", currentLevel);
    iceDuration = TLBMethods.getLong("ExtraAbilities.Moros.RazorRing.IceDuration", currentLevel);
    sourceRadius = TLBMethods.getDouble("ExtraAbilities.Moros.RazorRing.SourceRadius", currentLevel);
    ringRadius = TLBMethods.getDouble("ExtraAbilities.Moros.RazorRing.RingRadius", currentLevel);
    maxRingRadius = TLBMethods.getDouble("ExtraAbilities.Moros.RazorRing.MaxRingRadius", currentLevel);
    range = TLBMethods.getDouble("ExtraAbilities.Moros.RazorRing.Range", currentLevel);
    damage = TLBMethods.getDouble("ExtraAbilities.Moros.RazorRing.Damage", currentLevel);
    speed = TLBMethods.getDouble("ExtraAbilities.Moros.RazorRing.Speed", currentLevel);
    collisionRadius = TLBMethods.getDouble("ExtraAbilities.Moros.RazorRing.CollisionRadius", currentLevel);
  }

  private boolean prepare() {
    Block sourceBlock = null;
    if (bPlayer.canUseSubElement(Element.SubElement.ICE)) {
      sourceBlock = getIceSourceBlock(player, sourceRadius);
    }
    if (sourceBlock == null) {
      sourceBlock = getWaterSourceBlock(player, sourceRadius, false);
    }
    if (sourceBlock == null || (!isWater(sourceBlock) && !isIce(sourceBlock)) || !isTransparent(player, sourceBlock.getRelative(BlockFace.UP))) {
      return false;
    }
    if (!GeneralMethods.isAdjacentToThreeOrMoreSources(sourceBlock)) {
      sourceBlock.setType(Material.AIR);
    }
    source = new TempBlock(sourceBlock, Material.WATER);
    location = sourceBlock.getLocation();
    return true;
  }

  @Override
  public void progress() {
    if (!player.isSneaking() || !bPlayer.canBendIgnoreCooldowns(this)) {
      remove();
      return;
    }

    switch (stage) {
      case SOURCING -> progressSource();
      case FORMING_RING, RING_READY -> progressRing();
      case LAUNCHING -> progressLaunch();
    }
  }

  private void progressSource() {
    final Location eyeLoc = this.player.getEyeLocation();
    final double startAngle = this.player.getEyeLocation().getDirection().angle(new Vector(1, 0, 0));
    final double dx = RING_RADIUS * Math.cos(startAngle);
    final double dy = 0;
    final double dz = RING_RADIUS * Math.sin(startAngle);
    final Location setup = eyeLoc.clone().add(dx, dy, dz);

    if (!this.location.getWorld().equals(this.player.getWorld())) {
      this.remove();
      return;
    } else if (this.location.distanceSquared(setup) > this.range * this.range) {
      this.remove();
      return;
    }

    if (this.location.getBlockY() > setup.getBlockY()) {
      final Vector direction = new Vector(0, -1, 0);
      this.location = this.location.clone().add(direction);
    } else if (this.location.getBlockY() < setup.getBlockY()) {
      final Vector direction = new Vector(0, 1, 0);
      this.location = this.location.clone().add(direction);
    } else {
      final Vector direction = GeneralMethods.getDirection(this.location, setup).normalize();
      this.location = this.location.clone().add(direction);
    }

    if (this.location.distanceSquared(setup) <= 1) {
      this.source.revertBlock();
      this.source = null;
      stage = Stage.FORMING_RING;
    } else if (!this.location.getBlock().equals(this.source.getLocation().getBlock())) {
      this.source.revertBlock();
      this.source = null;
      final Block block = this.location.getBlock();
      if (!isTransparent(this.player, block)) {
        this.remove();
        return;
      }
      this.source = new TempBlock(this.location.getBlock(), Material.WATER);
    }
  }

  private void progressRing() {
    if (ThreadLocalRandom.current().nextInt(4) == 0) {
      playWaterbendingSound(this.location);
    }
    ThreadLocalRandom rand = ThreadLocalRandom.current();
    for (Location loc : iterateRing()) {
      if (isWater(loc.getBlock()) && GeneralMethods.isAdjacentToThreeOrMoreSources(loc.getBlock())) {
        Location blockCenter = loc.getBlock().getLocation().add(0.5, 0.5, 0.5);
        ParticleEffect.WATER_BUBBLE.display(blockCenter, 5, rand.nextDouble(), rand.nextDouble(), rand.nextDouble(), 0);
      }
    }
    if (this.angle < 220) {
      this.angle += 20;
    } else {
      stage = Stage.RING_READY;
      
      // For single player, allow launching after forming the ring
      if (!cooperative) {
        // Wait a short moment before auto-launching
        ProjectKorra.plugin.getServer().getScheduler().runTaskLater(ProjectKorra.plugin, () -> {
          if (stage == Stage.RING_READY && player.isSneaking()) {
            launchSolo();
          }
        }, 10L);
      }
    }

    this.formRing();
    if (ringBlocks.isEmpty()) {
      this.remove();
    }
  }

  private void formRing() {
    this.clearRing();
    this.startAngle += 30;

    Collection<Block> doneBlocks = new HashSet<>();
    for (Location loc : iterateRing()) {
      Block block = loc.getBlock();
      if (isTransparent(this.player, block) && doneBlocks.add(block)) {
        ringBlocks.add(new TempBlock(block, Material.WATER));
      }
    }
  }

  private void clearRing() {
    ringBlocks.forEach(TempBlock::revertBlock);
    ringBlocks.clear();
  }

  private Collection<Location> iterateRing() {
    Collection<Location> locs = new ArrayList<>();
    for (double theta = this.startAngle; theta < this.angle + this.startAngle; theta += 20) {
      double phi = Math.toRadians(theta);
      locs.add(player.getEyeLocation().add(RING_RADIUS * Math.cos(phi), 0, RING_RADIUS * Math.sin(phi)));
    }
    return List.copyOf(locs);
  }

  // New method for solo launching
  private void launchSolo() {
    if (stage != Stage.RING_READY) {
      return;
    }
    stage = Stage.LAUNCHING;
    Location origin = player.getEyeLocation();
    bPlayer.addCooldown(this);
    clearRing();
    for (double theta = startAngle; theta < angle + startAngle; theta += 20) {
      double phi = Math.toRadians(theta);
      if (Math.abs(theta - this.startAngle) < 10) {
        this.location = origin.add(RING_RADIUS * Math.cos(phi), 0, RING_RADIUS * Math.sin(phi));
      }
    }
    Location targetLoc = GeneralMethods.getTargetedLocation(player, range);
    direction = GeneralMethods.getDirection(location, targetLoc).normalize().multiply(speed);
    location.setDirection(direction);
  }

  private boolean launch(Player other) {
    if (stage != Stage.RING_READY) {
      return false;
    }
    stage = Stage.LAUNCHING;
    Location origin = player.getEyeLocation();
    bPlayer.addCooldown(this);
    setPlayer(other);
    clearRing();
    for (double theta = startAngle; theta < angle + startAngle; theta += 20) {
      double phi = Math.toRadians(theta);
      if (Math.abs(theta - this.startAngle) < 10) {
        this.location = origin.add(RING_RADIUS * Math.cos(phi), 0, RING_RADIUS * Math.sin(phi));
      }
    }
    Location targetLoc = GeneralMethods.getTargetedLocation(player, range);
    direction = GeneralMethods.getDirection(location, targetLoc).normalize().multiply(speed);
    location.setDirection(direction);
    return true;
  }

  private void freeze() {
    if (freeze || stage != Stage.LAUNCHING || !bPlayer.canUseSubElement(Element.SubElement.ICE) || !bPlayer.canBendIgnoreCooldowns(this)) {
      return;
    }
    freeze = true;
  }

  public static void freeze(Player player) {
    RazorRing ring = CoreAbility.getAbility(player, RazorRing.class);
    if (ring != null) {
      ring.freeze();
    }
  }

  private void progressLaunch() {
    if (distanceTravelled >= range) {
      remove();
      return;
    }
    location = location.add(direction);
    distanceTravelled += speed;
    if (ringRadius < maxRingRadius) {
      ringRadius += 0.15;
    }
    if (!validLocation(location) || !validLocation(location.clone().add(direction.clone().multiply(ringRadius * 0.7)))) {
      remove();
      return;
    }
    drawArc(ringRadius);
    if (ringBlocks.size() < 1.6 * ringRadius) {
      hit = true;
      remove();
      return;
    }
    Map<Entity, BoundingBox> entityMap = GeneralMethods.getEntitiesAroundPoint(location, ringRadius + 2).stream()
      .filter(this::validTarget).collect(Collectors.toMap(Function.identity(), Entity::getBoundingBox));
    for (TempBlock block : ringBlocks) {
      BoundingBox box = BoundingBox.of(block.getBlock()).expand(0.5);
      for (var entry : entityMap.entrySet()) {
        Entity entity = entry.getKey();
        BoundingBox entityBox = entry.getValue();
        if (!affectedEntities.contains(entity) && box.overlaps(entityBox)) {
          affectedEntities.add(entity);
          // Apply increased damage if in cooperative mode
          double finalDamage = cooperative ? damage * 1.5 : damage;
          DamageHandler.damageEntity(entity, player, finalDamage, this);
          hit = true;
        }
      }
    }
    if (hit) {
      remove();
    }
  }

  private boolean validLocation(Location loc) {
    if (GeneralMethods.isRegionProtectedFromBuild(this, loc)) {
      return false;
    }
    if (!isTransparent(player, loc.getBlock()) || GeneralMethods.checkDiagonalWall(loc, direction)) {
      hit = true;
      return false;
    }
    return true;
  }

  private boolean validTarget(Entity entity) {
    if (entity.getUniqueId().equals(player.getUniqueId())) {
      return false;
    }
    if (entity instanceof LivingEntity) {
      if (entity instanceof ArmorStand) {
        return false;
      }
      return !GeneralMethods.isRegionProtectedFromBuild(this, entity.getLocation());
    }
    return false;
  }

  private void drawArc(double radius) {
    clearRing();
    Collection<Block> doneBlocks = new HashSet<>();
    double pitch = Math.toRadians(location.getPitch() - 90);
    double yaw = Math.toRadians(location.getYaw() + 90);
    double cos = Math.cos(-yaw);
    double sin = Math.sin(-yaw);
    
    // Adjust the number of blocks based on cooperative mode
    double stepSize = cooperative ? Math.PI / 24 : Math.PI / 18;
    
    for (double theta = pitch; theta <= Math.PI + pitch; theta += stepSize) {
      final Vector temp = new Vector(radius * Math.cos(theta), radius * -Math.sin(theta), 0);
      rotateAroundAxisY(temp, cos, sin);
      Block block = location.clone().add(temp).getBlock();
      if (isTransparent(player, block) && doneBlocks.add(block)) {
        ringBlocks.add(new TempBlock(block, Material.WATER));
      }
    }
  }

  private void rotateAroundAxisY(Vector v, double cos, double sin) {
    double x = v.getX() * cos + v.getZ() * sin;
    double z = v.getX() * -sin + v.getZ() * cos;
    v.setX(x).setZ(z);
  }

  @Override
  public void remove() {
    super.remove();
    Collection<Block> freezeBlocks = List.of();
    if (stage == Stage.LAUNCHING && freeze && hit) {
      freezeBlocks = ringBlocks.stream().map(TempBlock::getBlock).toList();
    }
    this.clearRing();
    if (this.source != null) {
      this.source.revertBlock();
    }
    if (stage != Stage.LAUNCHING) {
      bPlayer.addCooldown(this, cooldown / 2);
    }
    freezeBlocks.forEach(b -> new TempBlock(b, Material.ICE.createBlockData(), iceDuration));
  }

  @Override
  public long getCooldown() {
    return cooldown;
  }

  @Override
  public Location getLocation() {
    return location;
  }

  @Override
  public List<Location> getLocations() {
    return ringBlocks.stream().map(TempBlock::getLocation).collect(Collectors.toList());
  }

  @Override
  public boolean isCollidable() {
    return stage != Stage.SOURCING;
  }

  @Override
  public double getCollisionRadius() {
    return collisionRadius;
  }

  @Override
  public void handleCollision(Collision collision) {
    if (collision.getAbilitySecond() instanceof EarthSmash smash) {
      Map<Location, BlockData> smashBlocks = smash.getBlocks().stream()
        .collect(Collectors.toMap(b -> b.getLocation().add(0.5, 0, 0.5), Block::getBlockData));
      if (!smashBlocks.isEmpty()) {
        ProjectKorra.plugin.getServer().getScheduler().runTaskLater(ProjectKorra.plugin, () -> {
          for (var entry : smashBlocks.entrySet()) {
            Vector vel = gaussianVelocity(0.2, 0.1, 0.2);
            new BendingFallingBlock(entry.getKey(), entry.getValue(), vel, this, true, 5000);
          }
        }, 1);
      }
    } else if (collision.getAbilitySecond() instanceof Combustion combustion) {
      Combustion.attemptExplode(combustion.getPlayer());
    }
    if (collision.getAbilitySecond().getElement().equals(Element.FIRE) && collision.isRemovingFirst()) {
      for (TempBlock tb : ringBlocks) {
        Location spawnLoc = tb.getLocation().add(0.5, 0.5, 0.5);
        spawnLoc.getWorld().playSound(spawnLoc, Sound.BLOCK_FIRE_EXTINGUISH, 0.5F, 1);
        ParticleEffect.CLOUD.display(spawnLoc, 5, 0.3, 0.3, 0.3);
      }
    }
    super.handleCollision(collision);
  }

  private Vector gaussianVelocity(double offsetX, double offsetY, double offsetZ) {
    ThreadLocalRandom r = ThreadLocalRandom.current();
    return new Vector(r.nextGaussian() * offsetX, r.nextGaussian() * offsetY, r.nextGaussian() * offsetZ);
  }

  @Override
  public String getName() {
    return "RazorRing";
  }

  @Override
  public String getDescription() {
    return "Create a ring of water that cuts through enemies. When used alone, you can launch the ring yourself. When teaming up with another waterbender, the ring becomes larger and more powerful.";
  }

  @Override
  public String getInstructions() {
    return "Hold sneak to create a ring of water around you. Release sneak to launch the ring forward. If another waterbender is nearby with RazorRing active, you can combine your abilities for a more powerful attack.";
  }

  @Override
  public boolean isHarmlessAbility() {
    return false;
  }

  @Override
  public boolean isSneakAbility() {
    return true;
  }

  @Override
  public String getAuthor() {
    return "Moros";
  }

  @Override
  public String getVersion() {
    return "1.0.0";
  }

  private static RazorRingListener listener;

  @Override
  public void load() {
    if (listener != null) {
      listener.unregister();
    }
    listener = new RazorRingListener();
    ProjectKorra.plugin.getServer().getPluginManager().registerEvents(listener, ProjectKorra.plugin);
    ProjectKorra.log.info(getName() + " " + getVersion() + " by " + getAuthor() + " loaded!");

    ConfigManager.getConfig().addDefault("ExtraAbilities.Moros.RazorRing.Cooldown", 16000);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Moros.RazorRing.IceDuration", 10000);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Moros.RazorRing.SourceRadius", 8.0);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Moros.RazorRing.RingRadius", 1.5);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Moros.RazorRing.MaxRingRadius", 3.0);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Moros.RazorRing.Range", 26.0);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Moros.RazorRing.Damage", 6.0);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Moros.RazorRing.Speed", 1.0);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Moros.RazorRing.CollisionRadius", 1.5);
    ConfigManager.defaultConfig.save();

    ProjectKorra.plugin.getServer().getScheduler().runTaskLater(ProjectKorra.plugin, this::setupCollisions, 5);
  }

  private void setupCollisions() {
    CoreAbility mainAbil = CoreAbility.getAbility(RazorRing.class);
    CoreAbility wof = CoreAbility.getAbility(WallOfFire.class);
    CoreAbility flameRush = CoreAbility.getAbility("FlameRush");
    CoreAbility combustion = CoreAbility.getAbility(Combustion.class);
    CoreAbility esmash = CoreAbility.getAbility(EarthSmash.class);
    ProjectKorra.getCollisionManager().addCollision(new Collision(mainAbil, mainAbil, true, true));
    ProjectKorra.getCollisionManager().addCollision(new Collision(mainAbil, flameRush, true, true));
    ProjectKorra.getCollisionManager().addCollision(new Collision(mainAbil, combustion, true, true));
    ProjectKorra.getCollisionManager().addCollision(new Collision(mainAbil, wof, true, false));
    ProjectKorra.getCollisionManager().addCollision(new Collision(mainAbil, esmash, false, true));
  }

  @Override
  public void stop() {
    ProjectKorra.log.info(getName() + " " + getVersion() + " by " + getAuthor() + " disabled!");
    if (listener != null) {
      listener.unregister();
    }
    listener = null;
  }
}
