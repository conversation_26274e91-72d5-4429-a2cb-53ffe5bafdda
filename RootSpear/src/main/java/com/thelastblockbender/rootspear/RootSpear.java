package com.thelastblockbender.rootspear;

import org.bukkit.Color;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle.DustOptions;
import org.bukkit.block.Block;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.util.Vector;

import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.PlantAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsMethods;

public class RootSpear extends PlantAbility implements AddonAbility {

    // Configuration variables
    private long chargeTime;
    private long holdTime;
    private double damage;
    private double range;
    private double speed;
    private double offsetRight;
    private double offsetAbove;
    private long cooldown;
    private double maxDistance;
    private double control;

    // Instance variables
    private Player player;
    private State state;
    private long startTime;
    private Location spearLocation;
    private SpearProjectile projectile;

    private Location location;
    private Vector direction;
    private double traveled = 0;
    private boolean done = false;

    private enum State { CHARGING, FORMED, LAUNCHED }

    public RootSpear(final Player player) {
        super(player);
        this.player = player;
        loadConfig();
        if (!isValid() || this.bPlayer.isOnCooldown(this)) {
            this.remove();
            return;
        }
        initialize();
    }

    private void loadConfig() {
        int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
        long currentLevel = TLBMethods.limitLevels(player, statLevel);

        chargeTime = TLBMethods.getLong("ExtraAbilities.Plant.RootSpear.ChargeTime", currentLevel);
        holdTime =TLBMethods.getLong("ExtraAbilities.Plant.RootSpear.HoldTime", currentLevel);
        damage = TLBMethods.getDouble("ExtraAbilities.Plant.RootSpear.Damage", currentLevel);
        range = TLBMethods.getDouble("ExtraAbilities.Plant.RootSpear.Range", currentLevel);
        speed = TLBMethods.getDouble("ExtraAbilities.Plant.RootSpear.Speed", currentLevel);
        offsetRight = TLBMethods.getDouble("ExtraAbilities.Plant.RootSpear.OffsetRight", currentLevel);
        offsetAbove = TLBMethods.getDouble("ExtraAbilities.Plant.RootSpear.OffsetAbove", currentLevel);
        cooldown = TLBMethods.getLong("ExtraAbilities.Plant.RootSpear.Cooldown", currentLevel);
        maxDistance = TLBMethods.getDouble("ExtraAbilities.Plant.RootSpear.MaxDistance", currentLevel);
        control = TLBMethods.getDouble("ExtraAbilities.Plant.RootSpear.Control", currentLevel);
    }

    private boolean isValid() {
        return player != null && this.bPlayer.canBend(this) && (isValidBlock() || hasNearbyPlants());
    }

    public boolean isValidBlock() {
        Block block = player.getLocation().subtract(0, 0.1, 0).getBlock();
        if (block.getType() == Material.GRASS_BLOCK) {
            return true;
        }
        else if (hasNearbyPlants()) {
            return true;
        }
        else { return false; }
    }

    private boolean hasNearbyPlants() {
        // Check for nearby grass plants within the charging radius
        Location playerLoc = player.getLocation();
        int radius = (int)Math.ceil(maxDistance);
        boolean found = false;
        
        for (int x = -radius; x <= radius; x++) {
            for (int z = -radius; z <= radius; z++) {
                // Skip blocks outside the circular radius
                if (x*x + z*z > maxDistance*maxDistance) continue;
                
                // Check for grass plants with all possible names
                Block block = playerLoc.getBlock().getRelative(x, 0, z);
                Material type = block.getType();
                
                // Try all possible grass material names
                if (type.name().contains("GRASS") || type.name().contains("FERN") || 
                    type.name().contains("VINE") || type.name().contains("PLANT") ||
                    type.name().contains("FLOWER") || type.name().contains("SAPLING")) {
                   
                    found = true;
                }
            }
        }
        
        
        return found;
    }

    private void initialize() {
        state = State.CHARGING;
        startTime = System.currentTimeMillis();
        if (isValidBlock()) {
            this.start();
        }
    }

    @Override
    public void progress() {
        if (player == null || !player.isOnline()) {
            remove();
            return;
        }
        switch (state) {
            case CHARGING:
                handleCharging();
                break;
            case FORMED:
                handleFormed();
                break;
            case LAUNCHED:
                handleLaunched();
                break;
        }
    }

    private void handleCharging() {
        if (!player.isSneaking() || !isValidBlock()) {
            remove();
            return;
        }
        
        // Update spear location before showing particles
        updateSpearLocation();
        
        if (System.currentTimeMillis() - startTime >= chargeTime) {
            state = State.FORMED;
            startTime = System.currentTimeMillis();
        } else {
            showChargingParticles();
        }
    }

    private void handleFormed() {
        if (!player.isSneaking()) {
            remove();
            return;
        }
        updateSpearLocation();
        showFormedSpear();
    }

    private void handleLaunched() {
        if (projectile == null || projectile.isDone()) {
            remove();
        } else {
            projectile.progress();
        }
    }

    private void updateSpearLocation() {
        Location eyeLoc = player.getEyeLocation().clone();
        Vector right = new Vector(-eyeLoc.getDirection().getZ(), 0, eyeLoc.getDirection().getX()).normalize();
        spearLocation = eyeLoc.add(right.multiply(offsetRight)).add(0, offsetAbove, 0);
        spearLocation.setDirection(eyeLoc.getDirection());
    }

    private void showChargingParticles() {
        // Calculate progress percentage (0.0 to 1.0)
        double progress = (double)(System.currentTimeMillis() - startTime) / chargeTime;
        progress = Math.min(progress, 1.0); // Cap at 1.0
        
        // If not on valid ground, source from nearby plants
        if (!isValidBlock() && hasNearbyPlants()) {
            showSourceParticles(progress);
        }
        
        // Create particles around the player's feet that move toward the spear location
        for (int i = 0; i < 5; i++) {
            // Random angle and distance (up to maxDistance blocks)
            double angle = Math.random() * 2 * Math.PI;
            double distance = Math.random() * maxDistance * (1.0 - progress); // Distance decreases as progress increases
            
            // Calculate ground position
            Location groundPos = player.getLocation().clone().add(
                Math.cos(angle) * distance,
                0.1, // Slightly above ground
                Math.sin(angle) * distance
            );
            
            // Show ground particles with minimal spread - mix green and brown
            if (Math.random() < 0.6) {
                // Green particles (60% chance) - now using oak leaves texture
                ParticleEffect.BLOCK_DUST.display(groundPos, 1, 0, 0, 0, 0, Material.OAK_LEAVES.createBlockData());
            } else {
                // Brown particles (40% chance)
                DustOptions brownDust = new DustOptions(Color.fromRGB(101, 67, 33), 1.0F);
                ParticleEffect.REDSTONE.display(groundPos, 1, 0, 0, 0, 0, brownDust);
            }
            
            // As progress increases, show particles moving up toward spear location
            if (progress > 0.5) {
                // Calculate how far up the particles should be (0 to 1 scale)
                double heightProgress = (progress - 0.5) * 2.0; // 0.0 to 1.0 during second half
                
                // Interpolate between ground position and final spear position
                Vector groundVector = groundPos.toVector();
                Vector spearVector = spearLocation.toVector();
                Vector interpolated = groundVector.clone().add(
                    spearVector.clone().subtract(groundVector).multiply(heightProgress)
                );
                
                Location particleLoc = interpolated.toLocation(player.getWorld());
                
                // Mix green and brown particles for rising effect too
                if (Math.random() < 0.7) {
                    // Green particles (70% chance) - now using oak leaves texture
                    ParticleEffect.BLOCK_DUST.display(particleLoc, 1, 0, 0, 0, 0, Material.OAK_LEAVES.createBlockData());
                } else {
                    // Brown particles (30% chance)
                    DustOptions darkBrownDust = new DustOptions(Color.fromRGB(85, 57, 29), 1.0F);
                    ParticleEffect.REDSTONE.display(particleLoc, 1, 0, 0, 0, 0, darkBrownDust);
                }
            }
        }
    }

    private void showSourceParticles(double progress) {
        // Find and display particles from nearby grass plants
        Location playerLoc = player.getLocation();
        int radius = (int)Math.ceil(maxDistance);
        
        for (int x = -radius; x <= radius; x++) {
            for (int z = -radius; z <= radius; z++) {
                // Skip blocks outside the circular radius
                if (x*x + z*z > maxDistance*maxDistance) continue;
                
                // Check for short_grass or tall_grass
                Block block = playerLoc.getBlock().getRelative(x, 0, z);
                Location sourceLoc = null;
                
                if (block.getType() == Material.SHORT_GRASS || block.getType() == Material.TALL_GRASS) {
                    sourceLoc = block.getLocation().add(0.5, 0.5, 0.5);
                } else {
                    // Also check one block above for taller plants
                    Block blockAbove = block.getRelative(0, 1, 0);
                    if (blockAbove.getType() == Material.SHORT_GRASS || blockAbove.getType() == Material.TALL_GRASS) {
                        sourceLoc = blockAbove.getLocation().add(0.5, 0.5, 0.5);
                    }
                }
                
                // If we found a source, show particles flowing from it
                if (sourceLoc != null && Math.random() < 0.3) {  // Only show particles for some sources each tick
                    // Calculate vector from source to spear location
                    Vector sourceToSpear = spearLocation.toVector().subtract(sourceLoc.toVector());
                    double distance = sourceToSpear.length();
                    sourceToSpear.normalize();
                    
                    // Show particles along the path from source to spear
                    double particleDistance = distance * progress;
                    if (particleDistance > 0.5) {  // Only show particles once they've moved a bit
                        Location particleLoc = sourceLoc.clone().add(sourceToSpear.clone().multiply(particleDistance));
                        
                        // Green particles for the flowing effect
                        ParticleEffect.BLOCK_DUST.display(particleLoc, 1, 0.1, 0.1, 0.1, 0, Material.OAK_LEAVES.createBlockData());
                        
                        // As a special effect, make the source plants shake a bit
                        if (Math.random() < 0.1) {
                            ParticleEffect.BLOCK_DUST.display(sourceLoc, 2, 0.2, 0.2, 0.2, 0, Material.OAK_LEAVES.createBlockData());
                        }
                    }
                }
            }
        }
    }

    private void showFormedSpear() {
        Vector dir = spearLocation.getDirection().normalize();
        
        for (double i = 0; i < 3.0; i += 0.1) {
            Location particleLoc = spearLocation.clone().add(dir.clone().multiply(i));
            
            DustOptions dustOptions;
            // Randomly choose between green and brown particles
            if (Math.random() < 0.3) {
                // 30% chance of green
                dustOptions = new DustOptions(Color.fromRGB(60, 179, 113), 1.0F);
            } else {
                // 70% chance of brown
                dustOptions = new DustOptions(Color.fromRGB(85, 57, 29), 1.0F);
            }
            
            // Display with minimal spread for a tight line
            ParticleEffect.REDSTONE.display(particleLoc, 1, .1, .1, .1, 0, dustOptions);
        }
    }

    public void launchSpear() {
        if (state != State.FORMED) return;
        state = State.LAUNCHED;
        bPlayer.addCooldown(this);
        // Launch from the spear's location with gradual alignment to player's eye direction
        Vector initialDirection = spearLocation.getDirection().normalize().multiply(speed);
        projectile = new SpearProjectile(spearLocation.clone(), initialDirection, damage, range, control);
    }

    @Override
    public String getAuthor() {
        return "TLB";
    }
    
    @Override
    public String getVersion() {
        return "1.0";
    }

    @Override
    public void load() {
        ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new RootSpearListener(), ProjectKorra.plugin);
        ConfigManager.defaultConfig.get().addDefault("ExtraAbilities.Plant.RootSpear.ChargeTime", 2000); // 2 seconds in milliseconds
        ConfigManager.defaultConfig.get().addDefault("ExtraAbilities.Plant.RootSpear.HoldTime", 2000);
        ConfigManager.defaultConfig.get().addDefault("ExtraAbilities.Plant.RootSpear.Damage", 5);
        ConfigManager.defaultConfig.get().addDefault("ExtraAbilities.Plant.RootSpear.Range", 20);
        ConfigManager.defaultConfig.get().addDefault("ExtraAbilities.Plant.RootSpear.Speed", 1);
        ConfigManager.defaultConfig.get().addDefault("ExtraAbilities.Plant.RootSpear.OffsetRight", 0.5);
        ConfigManager.defaultConfig.get().addDefault("ExtraAbilities.Plant.RootSpear.OffsetAbove", 1.5);
        ConfigManager.defaultConfig.get().addDefault("ExtraAbilities.Plant.RootSpear.Cooldown", 5000);
        ConfigManager.defaultConfig.get().addDefault("ExtraAbilities.Plant.RootSpear.MaxDistance", 4.0);
        ConfigManager.defaultConfig.get().addDefault("ExtraAbilities.Plant.RootSpear.Control", 0.3);
        ConfigManager.defaultConfig.save();
    }

    @Override
    public void stop() {
        ProjectKorra.log.info(getName() + " " + getVersion() + " by " + getAuthor() + " stopped! ");
        super.remove();
    }

    @Override
    public String getName() {
        return "RootSpear";
    }

    @Override
    public String getDescription() {
        return "Draw out the roots from the ground and form a spear to throw at your enemies.";

    }

    @Override
    public String getInstructions() {
        return "While standing on grass, hold sneak to charge up the spear, then punch to throw it.";
    }

    // Getters and other required methods
    public Location getLocation() { return player.getLocation(); }
    public long getCooldown() { return cooldown; }
    public boolean isSneakAbility() { return true; }
    public boolean isHarmlessAbility() { return false; }


    private class SpearProjectile {
        private boolean done = false;
        private Location location;
        private Vector direction;
        private double damage;
        private double range;
        private double traveled = 0;

        // Gradual alignment variables (like FireShots)
        private double control; // Control factor for alignment strength

        public void setDone(boolean done) {
            this.done = true;
        }

        public SpearProjectile(Location start, Vector initialDir, double damage, double range, double control) {
            this.location = start.clone();
            this.direction = initialDir.clone();
            this.damage = damage;
            this.range = range;
            this.control = control;
        }

        public void progress() {
            if (done) return;

            // Gradual alignment with player's eye direction (like FireShots)
            Vector playerDirection = player.getEyeLocation().getDirection().normalize();
            Vector currentDirection = direction.clone().normalize();

            // Interpolate between current direction and player direction based on control
            Vector newDirection = currentDirection.clone().multiply(1.0 - control).add(playerDirection.multiply(control));
            direction = newDirection.normalize().multiply(speed);

            // Move the projectile
            location.add(direction);
            traveled += direction.length();

            // Check for end conditions
            if (traveled >= range || location.getBlock().getType().isSolid()) {
                done = true;
            } else {
                checkCollision();
            }
            
            // Create a spear-like appearance with mixed particles
            Vector dir = direction.clone().normalize();
            
            // Spear shaft (brown) - behind the tip
            for (int i = 1; i <= 3; i++) {
                Location shaftLoc = location.clone().subtract(dir.clone().multiply(i * 0.2));
                DustOptions brownDust = new DustOptions(Color.fromRGB(85, 57, 29), 1.0F);
                ParticleEffect.REDSTONE.display(shaftLoc, 1, 0, 0, 0, 0, brownDust);
            }
            
            // Spear tip (mix of green and brown)
            DustOptions greenDust = new DustOptions(Color.fromRGB(34, 139, 34), 1.0F);
            ParticleEffect.REDSTONE.display(location, 1, 0, 0, 0, 0, greenDust);
            
            // Trailing leaves/roots effect (lighter green)
            if (Math.random() < 0.7) {
                Location trailLoc = location.clone().subtract(dir.clone().multiply(0.8 + Math.random() * 0.6));
                Vector offset = new Vector(
                    (Math.random() - 0.5) * 0.2,
                    (Math.random() - 0.5) * 0.2,
                    (Math.random() - 0.5) * 0.2
                );
                trailLoc.add(offset);
                DustOptions lightGreenDust = new DustOptions(Color.fromRGB(144, 238, 144), 0.8F);
                ParticleEffect.REDSTONE.display(trailLoc, 1, 0, 0, 0, 0, lightGreenDust);
            }
            
            if (done) explode();
        }

        private void checkCollision() {
            for (Entity entity : location.getWorld().getNearbyEntities(location, 1, 1, 1)) {
                if (entity instanceof LivingEntity && !entity.equals(player)) {
                    ((LivingEntity) entity).damage(damage, player);
                    done = true;
                    break;
                }
            }
        }

        private void explode() {
            // Create an explosion with mixed green and brown particles
            for (int i = 0; i < 20; i++) {
                Vector randomDir = new Vector(
                    Math.random() * 2 - 1,
                    Math.random() * 2 - 1,
                    Math.random() * 2 - 1
                ).normalize().multiply(Math.random() * 0.8);
                
                Location particleLoc = location.clone().add(randomDir);
                
                // Mix of green and brown particles
                DustOptions dustOptions;
                if (Math.random() < 0.6) {
                    // Green particles (60% chance)
                    dustOptions = new DustOptions(Color.fromRGB(34, 139, 34), 1.0F);
                } else {
                    // Brown particles (40% chance)
                    dustOptions = new DustOptions(Color.fromRGB(101, 67, 33), 1.0F);
                }
                
                ParticleEffect.REDSTONE.display(particleLoc, 1, 0, 0, 0, 0, dustOptions);
            }
        }

        public boolean isDone() { 
            return done;
        }

        public void remove() {
            done = true;
        }

        
    }

    public void remove() { 
        super.remove();
        done = true; 
        if (projectile != null) {
            projectile.setDone(true);  // Use the setter
        }
    }
}
