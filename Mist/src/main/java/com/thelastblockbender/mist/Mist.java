package com.thelastblockbender.mist;

import java.util.List;
import java.util.Random;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.block.data.BlockData;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;

import com.projectkorra.projectkorra.Element;
import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.WaterAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.BlockSource;
import com.projectkorra.projectkorra.util.ClickType;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import com.projectkorra.projectkorra.waterbending.util.WaterReturn;

import net.kyori.adventure.text.Component;

public class Mist extends WaterAbility implements AddonAbility {
  private Location location;

  private long cooldown;
  private double originalRadius;
  private double radius;
  private double duration;
  private double density;
  private Random random;
  private Location fieldCenter;
  private double heightReductionFactor;
  private double originalHeightReductionFactor;
  private Entity mistMarker; //save the entity here so you can remove it later
  private Block sourceBlock;
  private double flattenVal; //tracks the gradual flattening of the mist field
  private World w; //the world

  private long startTime; //time at the start of the move
  private long lastTickTime; //time at the previous tick
  private Component markerName;

  public Mist(Player player) {
    super(player);
    if (!bPlayer.canBend(this)) return; //ensure player bending is valid

    setFields(); //import values
    if (prepare()) { //if prepare method runs without problems, add the cooldown and start the move.
      bPlayer.addCooldown(this);
      
      start();
    }
  }

  public void setFields() { //import values
    int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
    long currentLevel = TLBMethods.limitLevels(player, statLevel);

    cooldown = TLBMethods.getLong("ExtraAbilities.Thel.Water.Mist.Cooldown", currentLevel);
    radius = TLBMethods.getDouble("ExtraAbilities.Thel.Water.Mist.Radius", currentLevel);
    originalRadius = radius;
    duration = TLBMethods.getDouble("ExtraAbilities.Thel.Water.Mist.Duration", currentLevel);
    density = TLBMethods.getDouble("ExtraAbilities.Thel.Water.Mist.Density", currentLevel);
    heightReductionFactor = TLBMethods.getDouble("ExtraAbilities.Thel.Water.Mist.HeightReductionFactor",currentLevel);
    originalHeightReductionFactor = heightReductionFactor;
    startTime = System.currentTimeMillis(); //time at the start of the move
    lastTickTime = startTime;
    fieldCenter = player.getLocation(); //center of the mist effect (does not move with player)
    density = density * radius * radius * Math.PI * (radius * heightReductionFactor) / 13.816; //density formula. "density" inputted is just a 0-1 value for config reasons. the constant is an experimentally determined value that works well when density input is 1.
    w = player.getWorld();
  }

  private boolean isBurningEntity(Entity entity) {
    if (entity.getType() != EntityType.ARMOR_STAND && entity instanceof LivingEntity && entity.getFireTicks() > 0) {
      return true;
    }
    return false;
  }

  public void clearFlames() {

    //radius - 1 is used here just because it ends up looking a little nicer

    //iter through blocks in the radius (positive Y only)
    for (double x = -(radius-1); x <= radius-1; x++) { //pos & neg
      for (double y = 0; y <= radius-1; y++) { //pos only
        for (double z = -(radius-1); z <= radius-1; z++) { //pos & neg
          Location currentLocation = fieldCenter.clone().add(x, y, z);
          Block block = currentLocation.getBlock();

          //check if block is fire
          if (block.getType() == Material.FIRE) {
            block.setType(Material.AIR); //extinguish it (non temporary effect)
            w.playSound(player.getLocation(),Sound.BLOCK_FIRE_EXTINGUISH, 1f, 1f); 
            return; //stop looking for more flames (nice visual effect when flames go out one at a time)
          }
        }
      }
    }

    //now that physical flames have been extinguished, extinguish entities that are on fire
    List<Entity> entities = GeneralMethods.getEntitiesAroundPoint(location, radius).stream() //sort and filter list
      .filter(this::isBurningEntity)
      .toList();
    if (entities.size() > 0) {
      entities.get(0).setFireTicks(0);
      w.playSound(player.getLocation(),Sound.BLOCK_FIRE_EXTINGUISH, 1f, 1f); 
    }
  }
  public void spawnMistParticle() {
    //get random values
    double angle = random.nextDouble() * 2 * Math.PI;  //angle (rad)
    double distance = Math.sqrt(random.nextDouble()) * radius;  //random distance squared (allows uniform distribution)
    double height = random.nextDouble() * radius * heightReductionFactor; //Y coord relative to player, always positive

    //get positions
    double xOffset = Math.cos(angle) * distance;
    double zOffset = Math.sin(angle) * distance;
    
    double x = fieldCenter.getX() + xOffset;
    double z = fieldCenter.getZ() + zOffset;
    Location  nextPos = new Location(w,x,height + fieldCenter.getY(),z);

    BlockData fogData = Material.WHITE_STAINED_GLASS_PANE.createBlockData();
    w.spawnParticle(Particle.BLOCK_MARKER, nextPos, 1, 0, 0, 0, 0, fogData, true);
  }

  @Override
  public void progress() {

    if (duration - (System.currentTimeMillis() - startTime) > 4000) { //only spawn particles if at least 4 seconds remain (time it takes for the particles to decay)
      for (Integer i = 0; i < density; i++ ) {
      spawnMistParticle();
    }
    }

    flattenVal += (System.currentTimeMillis() - lastTickTime);
    radius = originalRadius * (1 + ((flattenVal / duration)/1.75)); //expands slowly!
    heightReductionFactor = originalHeightReductionFactor * ( 1 - (flattenVal / duration)); //reduces
    lastTickTime = System.currentTimeMillis();

    markerName = Component.text("MIST_"+radius);
    mistMarker.customName(markerName);

    clearFlames(); //clean out one fire block in the mist's region (done one at a time for aesthetic purposes)

    //check if mist should stop
    if (System.currentTimeMillis()-startTime >= duration) {
      remove();
      return;
      }
    
  }

  public boolean prepare() {
    location = player.getLocation();
    random = new Random();

    Block block = location.getBlock();
    if (block.isLiquid() || !isTransparent(block)) { //check if not standing in clear space
      return false;
    }

    //get water source
    if (!WaterReturn.hasWaterBottle(player)) {
			boolean allowIce = bPlayer.canUseSubElement(Element.SubElement.ICE);
			boolean allowPlant = bPlayer.canUseSubElement(Element.SubElement.PLANT);

			sourceBlock = BlockSource.getWaterSourceBlock(player, 8, ClickType.LEFT_CLICK, true, allowIce, allowPlant, allowIce, true);
			if (sourceBlock == null) {return false;}
			sourceBlock = player.getEyeLocation().clone().getBlock();
			location = sourceBlock.getLocation().clone();
    }
    
    //remove water bottle if used (it does not get returned, it evaporates permanently)
    if (WaterReturn.hasWaterBottle(player)) {
			WaterReturn.emptyWaterBottle(player);
		}

    //spawn mist interaction entity
    mistMarker = w.spawnEntity(player.getLocation().add(0,-1,0),EntityType.INTERACTION);
    markerName = Component.text("MIST_"+radius);
    mistMarker.customName(markerName);

    return true;
  }

  @Override
  public long getCooldown() {
    return cooldown;
  }

  @Override
  public Location getLocation() {
    return location;
  }

  @Override
  public String getName() {
    return "Mist";
  }

  @Override
  public String getDescription() {
    return "This ability allows a waterbender to flood the air around them with water vapour, shaping the environment to their advantage.";
  }

  @Override
  public String getInstructions() {
    return "Click on a water source and then hold shift to release a static cloud of mist. Within this mist, Condensation is extremely rapid, and flames cannot persist.";
  }

  @Override
	public boolean isSneakAbility() {
		return true;
	}
  
  @Override
  public boolean isHarmlessAbility() {
    return true;
  }

  @Override
  public String getAuthor() {
    return "Thel";
  }

  @Override
  public String getVersion() {
    return "v1.0.0";
  }

  @Override
  public void load() {
    ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new MistListener(), ProjectKorra.plugin);
    ProjectKorra.log.info(getName() + " " + getVersion() + " by " + getAuthor() + " loaded!");

    ConfigManager.getConfig().addDefault("ExtraAbilities.Thel.Water.Mist.Cooldown", 30000);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Thel.Water.Mist.Radius", 10);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Thel.Water.Mist.Duration", 15000);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Thel.Water.Mist.Density", 0.1);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Thel.Water.Mist.HeightReductionFactor",0.66);
    ConfigManager.defaultConfig.save();
  }

  @Override
  public void stop() {
    ProjectKorra.log.info(getName() + " " + getVersion() + " by " + getAuthor() + " disabled!");
  }

  @Override
  public void remove() {
    mistMarker.remove();
    super.remove();
  }
}