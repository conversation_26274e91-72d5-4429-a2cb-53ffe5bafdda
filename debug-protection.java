// Add this to a test plugin to debug region protection
import com.projectkorra.projectkorra.region.RegionProtectionBase;
import java.lang.reflect.Field;
import java.util.List;

public void debugRegionProtection() {
    try {
        // Get the list of registered region protectors
        Field field = RegionProtectionBase.class.getDeclaredField("instances");
        field.setAccessible(true);
        List<RegionProtectionBase> instances = (List<RegionProtectionBase>) field.get(null);
        
        System.out.println("Registered region protectors:");
        for (RegionProtectionBase protector : instances) {
            System.out.println("- " + protector.getName());
        }
        
        // Check if Kingdoms is registered
        boolean kingdomsFound = instances.stream()
            .anyMatch(p -> p.getName().equals("Kingdoms"));
        
        System.out.println("Kingdoms protection registered: " + kingdomsFound);
        
    } catch (Exception e) {
        e.printStackTrace();
    }
}
