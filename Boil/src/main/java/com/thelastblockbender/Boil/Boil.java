package com.thelastblockbender.Boil;

import java.util.ArrayList;

import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;

import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.ComboAbility;
import com.projectkorra.projectkorra.ability.CoreAbility;
import com.projectkorra.projectkorra.ability.FireAbility;
import com.projectkorra.projectkorra.ability.util.ComboManager;
import com.projectkorra.projectkorra.attribute.Attribute;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.ClickType;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.waterbending.WaterSpout;

public class Boil extends FireAbility implements AddonAbility, ComboAbility {

    // Configuration
    @Attribute(Attribute.COOLDOWN)
    private long cooldown;
    @Attribute(Attribute.DURATION)
    private long duration;
    @Attribute(Attribute.RADIUS)
    private double maxRadius;
    @Attribute(Attribute.DAMAGE)
    private double damage;
    private int airThreshold;
    private long maxChargeTime;

    // Runtime
    private Location origin;
    private long chargeStart;
    private double currentRadius;
    private long startTime;
    private int damageCounter;

    public Boil(Player player) {
        super(player);


        // Check if ability is enabled
        if (!getConfig().getBoolean("Abilities.Fire.Boil.Enabled")) {
            return;
        }

        if (!bPlayer.canBendIgnoreBinds(this)) {
            return;
        }

        Block target = player.getTargetBlockExact(10);

        // Try alternative water detection methods
        if (target == null || target.getType() != Material.WATER) {
            target = player.getTargetBlock(null, 10);

            if (target == null || target.getType() != Material.WATER) {
                if (target != null && target.getType() == Material.WATER_CAULDRON) {
                } else {
                    return;
                }
            }
        }

        this.origin = target.getLocation();
        loadConfig();

        this.startTime = System.currentTimeMillis();
        this.chargeStart = System.currentTimeMillis();
        this.currentRadius = 1;
        this.damageCounter = 0;

        start();
    }

    private void loadConfig() {
        this.cooldown = getConfig().getLong("Abilities.Fire.Boil.Cooldown");
        this.duration = getConfig().getLong("Abilities.Fire.Boil.Duration");
        this.maxRadius = getConfig().getDouble("Abilities.Fire.Boil.MaxRadius");
        this.damage = getConfig().getDouble("Abilities.Fire.Boil.Damage");
        this.airThreshold = getConfig().getInt("Abilities.Fire.Boil.AirThreshold");
        this.maxChargeTime = getConfig().getLong("Abilities.Fire.Boil.ChargeTime");
    }

    @Override
    public void progress() {
        if (!player.isOnline() || player.isDead()) {
            remove();
            return;
        }

        // Check expiration
        if (System.currentTimeMillis() > startTime + duration) {
            remove();
            return;
        }

        // Check air level
        if (player.getRemainingAir() <= airThreshold) {
            remove();
            return;
        }

        // Update radius based on charge
        if (player.isSneaking()) {
            long chargeDuration = System.currentTimeMillis() - chargeStart;
            currentRadius = Math.min(maxRadius, 1 + (chargeDuration / (double) maxChargeTime) * (maxRadius - 1));
        } else {
            chargeStart = System.currentTimeMillis();
        }

        // Affect area
        int radius = (int) Math.ceil(currentRadius);
        for (int x = -radius; x <= radius; x++) {
            for (int z = -radius; z <= radius; z++) {
                for (int y = -1; y <= 1; y++) {
                    Location loc = origin.clone().add(x, y, z);
                    if (loc.getBlock().getType() == Material.WATER || loc.getBlock().getType() == Material.WATER_CAULDRON) {
                        // Visuals
                        ParticleEffect.WATER_BUBBLE.display(loc, 1, 0.5f, 0.5f, 0.1f, 3);

                        // Damage entities
                        for (Entity entity : loc.getWorld().getNearbyEntities(loc, 0.5, 0.5, 0.5)) {
                            if (entity instanceof LivingEntity) {
                                ((LivingEntity) entity).damage(damage, player);
                            }
                        }

                        // Check for Waterspout users above
                        checkVerticalWaterspout(loc);
                    }
                }
            }
        }

        // Damage player if in water
        if (player.getLocation().getBlock().getType() == Material.WATER) {
            player.damage(damage);
        }

        // Apply damage every 5 ticks (0.25 damage * 4 = 1 damage per second)
        if (damageCounter++ % 5 == 0) {
            // Actual damage application handled above
        }
    }

    private void checkVerticalWaterspout(Location waterLoc) {
        for (int i = 1; i <= 15; i++) {
            Location checkLoc = waterLoc.clone().add(0, i, 0);
            for (Entity entity : checkLoc.getWorld().getNearbyEntities(checkLoc, 0.5, 0.5, 0.5)) {
                if (entity instanceof Player) {
                    Player target = (Player) entity;
                    CoreAbility wspout = CoreAbility.getAbility(target, WaterSpout.class);
                    if (wspout != null) {
                        wspout.remove();
                        target.sendMessage(ChatColor.BLUE + "Your Waterspout was disrupted by boiling water!");
                    }
                }
            }
        }
    }

    @Override
    public boolean isSneakAbility() {
        return true;
    }

    @Override
    public long getCooldown() {
        return cooldown;
    }

    @Override
    public Location getLocation() {
        return origin;
    }

    @Override
    public String getName() {
        return "Boil";
    }

    @Override
    public String getDescription() {
        return "Firebenders can boil water to damage enemies and disrupt waterbending techniques. " +
               "Hold sneak to increase the affected area.";
    }

    @Override
    public String getInstructions() {
        return "FireManipulation (Left Click) > HeatControl (Tap Sneak) > HeatControl (Hold Sneak) while looking at water";
    }

    @Override
    public ArrayList<ComboManager.AbilityInformation> getCombination() {
        final ArrayList<ComboManager.AbilityInformation> combo = new ArrayList<ComboManager.AbilityInformation>();
        combo.add(new ComboManager.AbilityInformation("FireManipulation", ClickType.LEFT_CLICK));
        combo.add(new ComboManager.AbilityInformation("HeatControl", ClickType.SHIFT_DOWN));
        combo.add(new ComboManager.AbilityInformation("HeatControl", ClickType.SHIFT_UP));
        combo.add(new ComboManager.AbilityInformation("HeatControl", ClickType.SHIFT_DOWN));
        return combo;
    }

    @Override
    public Object createNewComboInstance(final Player player) {
        return new Boil(player);
    }

    @Override
    public boolean isHarmlessAbility() {
        return false;
    }

    @Override
    public String getAuthor() {
        return "TLB";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @Override
    public void load() {
        ConfigManager.getConfig().addDefault("Abilities.Fire.Boil.Enabled", true);
        ConfigManager.getConfig().addDefault("Abilities.Fire.Boil.Cooldown", 7000);
        ConfigManager.getConfig().addDefault("Abilities.Fire.Boil.Duration", 5000);
        ConfigManager.getConfig().addDefault("Abilities.Fire.Boil.MaxRadius", 8);
        ConfigManager.getConfig().addDefault("Abilities.Fire.Boil.Damage", 0.25);
        ConfigManager.getConfig().addDefault("Abilities.Fire.Boil.AirThreshold", 150);
        ConfigManager.getConfig().addDefault("Abilities.Fire.Boil.ChargeTime", 3000);
        ProjectKorra.getCollisionInitializer().addComboAbility((CoreAbility)this);
        ProjectKorra.log.info("Enabled " + this.getName() + " by " + this.getAuthor());
    }

    @Override
    public void stop() {
        ProjectKorra.log.info("Disabled " + this.getName() + " by " + this.getAuthor());
    }
}
