package com.thelastblockbender.soundwave;

import java.util.HashSet;
import java.util.Random;
import java.util.Set;
import java.util.logging.Level;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.AirAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.DamageHandler;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import org.bukkit.Color;
import org.bukkit.Location;
import org.bukkit.Particle.DustOptions;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.util.Vector;

public class Soundwave extends AirAbility implements AddonAbility {
  private double t;
  private Location loc;
  private boolean isCharged;
  private long chargeTime;
  private long cooldown;
  private double damage;
  private double knockback;
  private int size;
  private int hits;
  private int maxHits;

  private final Set<Entity> affectedEntities = new HashSet<>();

  public Soundwave(final Player player) {
    super(player);
    if (!bPlayer.canBend(this) || hasAbility(player, Soundwave.class)) {
      return;
    }
    this.t = Math.PI / 4;
    this.setFields();

    this.start();
  }

  public void setFields() {
    int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
    long currentLevel = TLBMethods.limitLevels(player, statLevel);

    this.loc = this.player.getLocation().clone();
    this.damage = TLBMethods.getDouble("ExtraAbilities.EmeraldJelly.Air.Soundwave.Damage", currentLevel);
    this.knockback = TLBMethods.getDouble("ExtraAbilities.EmeraldJelly.Air.Soundwave.KnockBack", currentLevel);
    this.cooldown = TLBMethods.getLong("ExtraAbilities.EmeraldJelly.Air.Soundwave.Cooldown", currentLevel);
    this.chargeTime = TLBMethods.getLong("ExtraAbilities.EmeraldJelly.Air.Soundwave.ChargeTime", currentLevel);
    this.size = TLBMethods.getInt("ExtraAbilities.EmeraldJelly.Air.Soundwave.Size", currentLevel);
    this.maxHits = TLBMethods.getInt("ExtraAbilities.EmeraldJelly.Air.Soundwave.MaxHits", currentLevel);
  }

  @Override
  public long getCooldown() {
    return this.cooldown;
  }

  @Override
  public Location getLocation() {
    return null;
  }

  @Override
  public String getName() {
    return "Soundwave";
  }

  @Override
  public boolean isHarmlessAbility() {
    return false;
  }

  @Override
  public boolean isSneakAbility() {
    return true;
  }

  @Override
  public void progress() {
    if (!bPlayer.canBendIgnoreCooldowns(this)) {
      bPlayer.addCooldown(this);
      remove();
      return;
    }
    if (isCharged) {
      radialWave();
    } else {
      boolean charged = System.currentTimeMillis() > getStartTime() + chargeTime;
      if (player.isSneaking()) {
        Location chargeLoc = player.getLocation().add(0, 1, 0);
        if (charged) {
          Location chargeLoc2 = player.getEyeLocation().add(player.getLocation().getDirection().multiply(0.3));
          playAirbendingParticles(chargeLoc2, 1);
        }
        renderParticles(chargeLoc, 0.4);
        loc = player.getLocation();
      } else {
        if (!charged) {
          remove();
          isCharged = false;
          return;
        }
        isCharged = true;
      }
    }
  }

  private void renderParticles(Location center, double offset) {
    DustOptions options = new DustOptions(Color.fromRGB(115, 115, 115), 1);
    ParticleEffect.SPELL_MOB.display(center, 1, offset, offset, offset, options);
  }

  @Override
  public String getAuthor() {
    return "TLB (Original: EmeraldJelly)";
  }

  private void onEntityHit(Entity entity) {
    if (entity instanceof LivingEntity && entity.getUniqueId() != this.player.getUniqueId()) {
      if (!affectedEntities.contains(entity)) {
        affectedEntities.add(entity);
        hits++;
        DamageHandler.damageEntity(entity, this.damage, this);
        Vector dir = GeneralMethods.getDirection(this.loc, entity.getLocation()).add(new Vector(0, 0.5, 0));
        entity.setVelocity(dir.normalize().multiply(this.knockback));
        ((LivingEntity) entity).addPotionEffect(new PotionEffect(PotionEffectType.NAUSEA, 20, 1));
      }
    }
  }

  // TODO optimize trig
  public void radialWave() {
    if (this.isCharged) {
      this.t += Math.PI / 10;
      for (double theta = 0.0; theta <= 2 * Math.PI; theta += Math.PI / 64) {
        double x = this.t * Math.cos(theta);
        double y = 2.0 * Math.exp(-0.1 * this.t) * Math.sin(this.t) + 1.5;
        double z = this.t * Math.sin(theta);
        this.loc.add(x, y, z);
        renderParticles(loc, 0);
        GeneralMethods.getEntitiesAroundPoint(this.loc, 3.0).forEach(this::onEntityHit);
        this.loc.subtract(x, y, z);
        theta += Math.PI / 64;
        x = this.t * Math.cos(theta);
        y = 2.0 * Math.exp(-0.1 * this.t) * Math.sin(this.t) + 1.5;
        z = this.t * Math.sin(theta);
        this.loc.add(x, y, z);
        renderParticles(loc, 0);
        if (new Random().nextInt(7) == 0) {
          playAirbendingSound(this.loc);
        }
        GeneralMethods.getEntitiesAroundPoint(this.loc, 3.0).forEach(this::onEntityHit);
        this.loc.subtract(x, y, z);
      }
    }
    if (this.t > this.size || (maxHits > 0 && hits >= maxHits)) {
      bPlayer.addCooldown(this);
      remove();
    }
  }

  @Override
  public String getVersion() {
    return "v1.0.0";
  }

  @Override
  public String getDescription() {
    return "This advanced air bending move requires a lot of focus, concentration, and breathing. You are able to focus and send a shockwave of sound through the air knocking back your enemies while dealing damage. To do this, hold shift until you see air particles then release.";
  }

  @Override
  public void load() {
    ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new SoundwaveListener(), ProjectKorra.plugin);
    ProjectKorra.log.info(this.getName() + " " + "by " + this.getAuthor() + " " + this.getVersion() + " has been enabled!");
    ConfigManager.getConfig().addDefault("ExtraAbilities.EmeraldJelly.Air.Soundwave.Cooldown", 2500L);
    ConfigManager.getConfig().addDefault("ExtraAbilities.EmeraldJelly.Air.Soundwave.ChargeTime", 5000L);
    ConfigManager.getConfig().addDefault("ExtraAbilities.EmeraldJelly.Air.Soundwave.Damage", 1.5);
    ConfigManager.getConfig().addDefault("ExtraAbilities.EmeraldJelly.Air.Soundwave.KnockBack", 1.5);
    ConfigManager.getConfig().addDefault("ExtraAbilities.EmeraldJelly.Air.Soundwave.Size", 5);
    ConfigManager.getConfig().addDefault("ExtraAbilities.EmeraldJelly.Air.Soundwave.MaxHits", 0);
    ConfigManager.defaultConfig.save();
  }

  @Override
  public void stop() {
    ProjectKorra.plugin.getLogger().log(Level.INFO, "Soundwave by " + this.getAuthor() + " " + this.getVersion() + " has been disabled!");
  }
}
