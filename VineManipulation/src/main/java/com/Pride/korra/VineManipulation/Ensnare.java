package com.Pride.korra.VineManipulation;

import java.util.Iterator;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.entity.Creature;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.PlantAbility;
import com.projectkorra.projectkorra.airbending.Suffocate;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.TempBlock;

import net.md_5.bungee.api.ChatColor;

public class Ensnare extends PlantAbility implements AddonAbility {
   private static String path = "ExtraAbilities.Prride.VineManipulation.Ensnare.";
   private long cooldown;
   private long duration;
   private double radius;
   private long time;
   private Material material;
   private TempBlock snare;
   private Location location;

   public Ensnare(Player player) {
      super(player);
      if (this.bPlayer.canBend(this) && this.bPlayer.canPlantbend()) {
         if (this.bPlayer.isOnCooldown("VineEnsnare")) {
            this.remove();
         } else {
            this.cooldown = ConfigManager.getConfig().getLong(path + "Cooldown");
            this.duration = ConfigManager.getConfig().getLong(path + "Duration");
            this.radius = ConfigManager.getConfig().getDouble(path + "Radius");
            this.time = System.currentTimeMillis();
            this.location = player.getLocation();
            if (!player.isSneaking()) {
               this.start();
            }

         }
      }
   }

   public long getCooldown() {
      return this.cooldown;
   }

   public Location getLocation() {
      return null;
   }

   public String getName() {
      return "VineManipulation";
   }

   public boolean isHarmlessAbility() {
      return false;
   }

   public boolean isSneakAbility() {
      return false;
   }

   public void progress() {
      Iterator var2 = GeneralMethods.getEntitiesAroundPoint(this.location, this.radius).iterator();

      while(var2.hasNext()) {
         Entity entity = (Entity)var2.next();
         if (entity instanceof LivingEntity && entity.getEntityId() != this.player.getEntityId() && entity.isOnGround()) {
            this.material = entity.getLocation().getBlock().getRelative(BlockFace.DOWN).getType();
            if (!inPlants(entity) && !this.isPlantbendable(this.material)) {
               this.remove();
               return;
            }

            this.snare = new TempBlock(entity.getLocation().getBlock(), Material.TALL_GRASS);
            this.snare.setRevertTime(this.duration);
            this.snare.getLocation().getWorld().playSound(this.snare.getLocation(), Sound.BLOCK_GRASS_BREAK, 1.0F, 1.0F);
            this.ensnare(entity);
            entity.getLocation().getWorld().playSound(entity.getLocation(), Sound.BLOCK_GRASS_BREAK, 1.0F, 1.0F);
         }
      }

      if (System.currentTimeMillis() > this.time + this.duration) {
         this.remove();
         this.bPlayer.addCooldown("VineEnsnare", this.cooldown);
      }
   }

   public void ensnare(Entity entity) {
      if (entity instanceof Creature) {
         ((Creature)entity).setTarget((LivingEntity)null);
      }

      if (entity instanceof Player && Suffocate.isChannelingSphere((Player)entity)) {
         Suffocate.remove((Player)entity);
      }

      MovementHandler mh = new MovementHandler((LivingEntity)entity);
      mh.stop(this.duration / 1000L * 20L, ChatColor.GREEN + "* ENSNARED *");
   }

   public boolean isPlantbendable(Material material) {
      return isPlant(material) || isLeaves(material) || isFlowers(material) || isWood(material) || isPlantMaterial(material);
   }

   public static boolean isWood(Material material) {
      return material == Material.OAK_LOG || material == Material.SPRUCE_LOG;
   }

   public static boolean isFlowers(Material material) {
      return material == Material.CHORUS_FLOWER || material == Material.ROSE_BUSH || material == Material.DANDELION || material == Material.FLOWER_POT || material == Material.VINE;
   }

   public static boolean isPlantMaterial(Material material) {
      return material == Material.DIRT || material == Material.GRASS_BLOCK || material == Material.DIRT_PATH || material == Material.TALL_GRASS || material == Material.OAK_LEAVES;
   }

   private static boolean inPlants(Entity entity) {
      Block block = entity.getLocation().getBlock();
      return isPlant(block) && !TempBlock.isTempBlock(block);
   }

   public String getDescription() {
      return "Plantbenders are able to use their variety of plantbending to attack or trap people and mobs. \nSubsequently, many plantbenders are able to ensnare players and mobs within the ground using the roots burrowed beneath. \nPlantbenders can also use the plant life available around them in the environment to their advantage and launch vine attacks.\nPlantbenders are also able to use plants to wrap around and use as armor. Though frail, it provides protection against some attacks.";
   }

   public String getInstructions() {
      return "Left click to root players into the ground. Sneak on plants to manipulate them and release sneak to launch them. When plants are pulled and wrapped, left click to form armor.";
   }

   public String getAuthor() {
      return "Prride";
   }

   public String getVersion() {
      return "VineManipulation Build V1.2";
   }

   public void load() {
      ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new EnsnareListener(), ProjectKorra.plugin);
      ProjectKorra.log.info(this.getName() + " " + this.getVersion() + " by " + this.getAuthor() + " loaded! ");
      ConfigManager.getConfig().addDefault(path + "Cooldown", 7000);
      ConfigManager.getConfig().addDefault(path + "Duration", 2500);
      ConfigManager.getConfig().addDefault(path + "Radius", 15);
      ConfigManager.defaultConfig.save();
   }

   public void stop() {
      ProjectKorra.log.info(this.getName() + " " + this.getVersion() + " by " + this.getAuthor() + " stopped! ");
      super.remove();
   }
}
