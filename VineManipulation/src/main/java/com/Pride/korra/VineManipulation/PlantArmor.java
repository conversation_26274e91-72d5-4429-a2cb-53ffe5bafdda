package com.Pride.korra.VineManipulation;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.PlantAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import com.projectkorra.projectkorra.util.TempArmor;
import java.util.Iterator;
import org.bukkit.Color;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.LeatherArmorMeta;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;

public class PlantArmor extends PlantAbility implements AddonAbility {
   private static String path = "ExtraAbilities.Prride.VineManipulation.PlantArmor.";
   private long cooldown;
   private long duration;
   private PotionEffect oldAbsorbtion = null;
   private float goldHearts;
   private int maxGoldHearts;
   private boolean active = false;
   private TempArmor armor;
   private long currentLevel;

   public PlantArmor(Player player) {
      super(player);
      if (this.bPlayer.canBend(this)) {
         this.cooldown = ConfigManager.getConfig().getLong(path + "Cooldown");
         this.duration = ConfigManager.getConfig().getLong(path + "Duration");
         this.maxGoldHearts = ConfigManager.getConfig().getInt(path + "AbsorptionHearts");
         this.bPlayer.addCooldown(this);
         this.modify();
         this.formArmor();
         this.start();
      }
   }

   private void modify() {
      int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
      this.currentLevel = GeneralMethods.limitLevels(this.player, statLevel);
      this.duration = this.currentLevel * 1300L + 4000L;
      if (this.currentLevel <= 3L) {
         this.maxGoldHearts = 1;
      }

      if (this.currentLevel >= 4L && this.currentLevel <= 6L) {
         this.maxGoldHearts = 2;
      }

      if (this.currentLevel >= 7L) {
         this.maxGoldHearts = 3;
      }

   }

   public long getCooldown() {
      return this.cooldown;
   }

   public Location getLocation() {
      return null;
   }

   public String getName() {
      return "VineManipulation";
   }

   public boolean isHarmlessAbility() {
      return false;
   }

   public boolean isSneakAbility() {
      return true;
   }

   public void progress() {
      if (System.currentTimeMillis() - this.getStartTime() > this.duration) {
         this.player.getLocation().getWorld().playSound(this.player.getLocation(), Sound.BLOCK_STONE_BREAK, 2.0F, 1.0F);
         this.player.getLocation().getWorld().playSound(this.player.getLocation(), Sound.BLOCK_STONE_BREAK, 2.0F, 1.0F);
         this.player.getLocation().getWorld().playSound(this.player.getLocation(), Sound.BLOCK_STONE_BREAK, 2.0F, 1.0F);
         this.bPlayer.addCooldown(this);
         this.remove();
      } else {
         if (this.active && !this.player.hasPotionEffect(PotionEffectType.ABSORPTION)) {
            this.player.addPotionEffect(new PotionEffect(PotionEffectType.ABSORPTION, Integer.MAX_VALUE, 1, true, false));
            GeneralMethods.setAbsorbationHealth(this.player, this.goldHearts);
         }

         if (!this.player.isOnline() || this.player.isDead()) {
            this.bPlayer.addCooldown(this);
            this.remove();
         }
      }
   }

   public void formArmor() {
      ItemStack head = new ItemStack(Material.LEATHER_HELMET, 1);
      ItemStack chestplate = new ItemStack(Material.LEATHER_CHESTPLATE, 1);
      ItemStack leggings = new ItemStack(Material.LEATHER_LEGGINGS, 1);
      ItemStack boots = new ItemStack(Material.LEATHER_BOOTS, 1);
      LeatherArmorMeta metaHead = (LeatherArmorMeta)head.getItemMeta();
      LeatherArmorMeta metaChest = (LeatherArmorMeta)chestplate.getItemMeta();
      LeatherArmorMeta metaLegs = (LeatherArmorMeta)leggings.getItemMeta();
      LeatherArmorMeta metaBottom = (LeatherArmorMeta)boots.getItemMeta();
      metaHead.setColor(Color.fromRGB(4565115));
      metaChest.setColor(Color.fromRGB(4565115));
      metaLegs.setColor(Color.fromRGB(4565115));
      metaBottom.setColor(Color.fromRGB(4565115));
      head.setItemMeta(metaHead);
      chestplate.setItemMeta(metaChest);
      leggings.setItemMeta(metaLegs);
      boots.setItemMeta(metaBottom);
      ItemStack[] armors = new ItemStack[]{boots, leggings, chestplate, head};
      this.armor = new TempArmor(this.player, this.duration, this, armors);
      this.armor.setRemovesAbilityOnForceRevert(true);
      this.active = true;
      Iterator var11 = this.player.getActivePotionEffects().iterator();

      while(var11.hasNext()) {
         PotionEffect effect = (PotionEffect)var11.next();
         if (effect.getType() == PotionEffectType.ABSORPTION) {
            this.oldAbsorbtion = effect;
            this.player.removePotionEffect(PotionEffectType.ABSORPTION);
            break;
         }
      }

      int level = this.maxGoldHearts / 2 - 1 + this.maxGoldHearts % 2;
      this.player.addPotionEffect(new PotionEffect(PotionEffectType.ABSORPTION, Integer.MAX_VALUE, level, true, false));
      this.goldHearts = (float)(this.maxGoldHearts * 2);
      GeneralMethods.setAbsorbationHealth(this.player, this.goldHearts);
   }

   public void updateAbsorbtion() {
      (new BukkitRunnable() {
         public void run() {
            PlantArmor.this.goldHearts = GeneralMethods.getAbsorbationHealth(PlantArmor.this.player);
            if (PlantArmor.this.goldHearts < 0.9F) {
               PlantArmor.this.bPlayer.addCooldown(PlantArmor.this);
               PlantArmor.this.player.getLocation().getWorld().playSound(PlantArmor.this.player.getLocation(), Sound.BLOCK_GRASS_BREAK, 2.0F, 1.0F);
               PlantArmor.this.player.getLocation().getWorld().playSound(PlantArmor.this.player.getLocation(), Sound.BLOCK_GRASS_BREAK, 2.0F, 1.0F);
               PlantArmor.this.player.getLocation().getWorld().playSound(PlantArmor.this.player.getLocation(), Sound.BLOCK_GRASS_BREAK, 2.0F, 1.0F);
               PlantArmor.this.remove();
            }

         }
      }).runTaskLater(ProjectKorra.plugin, 1L);
   }

   public void remove() {
      super.remove();
      this.player.removePotionEffect(PotionEffectType.ABSORPTION);
      if (TempArmor.getTempArmorList(this.player).contains(this.armor)) {
         this.armor.revert();
      }

      if (this.oldAbsorbtion != null) {
         this.player.addPotionEffect(this.oldAbsorbtion);
      }

   }

   public String getAuthor() {
      return "Prride";
   }

   public String getVersion() {
      return "VineManipulation Build V1.2";
   }

   public String getDescription() {
      return "Plantbenders are able to use their variety of plantbending to attack or trap people and mobs. \nSubsequently, many plantbenders are able to ensnare players and mobs within the ground using the roots burrowed beneath. \nPlantbenders can also use the plant life available around them in the environment to their advantage and launch vine attacks.\nPlantbenders are also able to use plants to wrap around and use as armor. Though frail, it provides protection against some attacks.";
   }

   public String getInstructions() {
      return "Left click to root players into the ground. Sneak on plants to manipulate them and release sneak to launch them. When plants are pulled and wrapped, left click to form armor.";
   }

   public void load() {
      ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new PlantArmorListener(), ProjectKorra.plugin);
      ProjectKorra.log.info(this.getName() + " " + this.getVersion() + " by " + this.getAuthor() + " loaded! ");
      ConfigManager.getConfig().addDefault(path + "Cooldown", 15000);
      ConfigManager.getConfig().addDefault(path + "Duration", 60000);
      ConfigManager.getConfig().addDefault(path + "AbsorptionHearts", 2);
      ConfigManager.defaultConfig.save();
   }

   public void stop() {
      ProjectKorra.log.info(this.getName() + " " + this.getVersion() + " by " + this.getAuthor() + " stopped! ");
      super.remove();
   }
}
