package com.Pride.korra.VineManipulation;

import com.projectkorra.projectkorra.ability.CoreAbility;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageEvent;

public class PlantArmorListener implements Listener {
   @EventHandler(
      priority = EventPriority.NORMAL
   )
   public void onPlayerDamage(EntityDamageEvent event) {
      if (event.getEntity() instanceof Player) {
         Player player = (Player)event.getEntity();
         if (!event.isCancelled() && CoreAbility.getAbility(player, PlantArmor.class) != null) {
            PlantArmor plantarmor = (PlantArmor)CoreAbility.getAbility(player, PlantArmor.class);
            plantarmor.updateAbsorbtion();
         }
      }

   }
}
