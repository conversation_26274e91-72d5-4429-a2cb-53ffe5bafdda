package com.dreamerboy.korra;

import org.bukkit.event.HandlerList;
import com.projectkorra.projectkorra.util.ClickType;
import com.projectkorra.projectkorra.ability.util.ComboManager;
import java.util.ArrayList;
import java.util.Iterator;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Entity;
import com.projectkorra.projectkorra.util.ParticleEffect;
import org.bukkit.Material;
import com.projectkorra.projectkorra.util.TempBlock;
import com.projectkorra.projectkorra.GeneralMethods;
import org.bukkit.plugin.Plugin;
import org.bukkit.util.Vector;
import org.bukkit.scheduler.BukkitRunnable;
import com.projectkorra.projectkorra.ability.util.Collision;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.waterbending.SurgeWave;
import com.projectkorra.projectkorra.waterbending.WaterManipulation;
import com.projectkorra.projectkorra.waterbending.Torrent;
import com.projectkorra.projectkorra.ability.Ability;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.ability.CoreAbility;
import org.bukkit.entity.Player;
import org.bukkit.event.Listener;
import org.bukkit.Location;
import com.projectkorra.projectkorra.ability.ComboAbility;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.WaterAbility;

public class WaterBlock extends WaterAbility implements AddonAbility, ComboAbility
{
    private int count;
    private long cooldown;
    private long duration;
    private String path;
    private double radius;
    private double knockback;
    private double range;
    private double manipRange;
    private Location location;
    private boolean cooldownOnOutOfTheSightView;
    private boolean doneWork;
    private Listener WBL;
    
    public WaterBlock(final Player player) {
        super(player);
        this.count = 0;
        this.path = "ExtraAbilities.DreamerBoy.Water.WaterBlock.";
        this.doneWork = false;
        if (!this.bPlayer.canBendIgnoreBinds((CoreAbility)this) || hasAbility(player, (Class)WaterBlock.class)) {
            return;
        }
        this.setFields();
        this.start();
    }
    
    private void setFields() {
        this.cooldown = ConfigManager.getConfig().getLong(String.valueOf(this.path) + "Cooldown.Cooldown");
        this.cooldownOnOutOfTheSightView = ConfigManager.getConfig().getBoolean(String.valueOf(this.path) + "Cooldown.CooldownOnOutOfTheSightView");
        this.duration = ConfigManager.getConfig().getLong(String.valueOf(this.path) + "Duration");
        this.knockback = ConfigManager.getConfig().getDouble(String.valueOf(this.path) + "Knockback");
        this.radius = ConfigManager.getConfig().getDouble(String.valueOf(this.path) + "Radius");
        this.manipRange = ConfigManager.getConfig().getDouble(String.valueOf(this.path) + "Range.WaterManipulation");
        this.range = ConfigManager.getConfig().getDouble(String.valueOf(this.path) + "Range.Others");
    }
    
    public long getCooldown() {
        return this.cooldown;
    }
    
    public Location getLocation() {
        return this.player.getLocation();
    }
    
    public String getName() {
        return "WaterBlock";
    }
    
    public boolean isHarmlessAbility() {
        return false;
    }
    
    public boolean isSneakAbility() {
        return true;
    }
    
    public void remove() {
        if (this.doneWork) {
            this.bPlayer.addCooldown((Ability)this);
        }
        super.remove();
    }
    
    public void progress() {
        if (!this.player.isSneaking() || !this.bPlayer.canBendIgnoreBinds((CoreAbility)this) || (this.duration > 0L && System.currentTimeMillis() > this.getStartTime() + this.duration)) {
            this.remove();
            return;
        }
        this.collision();
    }
    
    public double getCollisionRadius() {
        return this.radius;
    }
    
    private void collision() {
        final CoreAbility Torrent = CoreAbility.getAbility((Class)Torrent.class);
        final CoreAbility WaterManipulation = CoreAbility.getAbility((Class)WaterManipulation.class);
        final CoreAbility WaterBlock = CoreAbility.getAbility((Class)WaterBlock.class);
        final CoreAbility Surge = CoreAbility.getAbility((Class)SurgeWave.class);
        final CoreAbility[] cAbilities = { Torrent, WaterManipulation, Surge };
        CoreAbility[] array;
        for (int length = (array = cAbilities).length, i = 0; i < length; ++i) {
            final CoreAbility cA = array[i];
            ProjectKorra.getCollisionManager().addCollision(new Collision(WaterBlock, cA, false, true));
        }
    }
    
    public void handleCollision(final Collision collision) {
        super.handleCollision(collision);
    }
    
    public boolean isDoneWork() {
        return this.doneWork;
    }
    
    public void setDoneWork(final boolean doneWork) {
        this.doneWork = doneWork;
    }
    
    public void setRange(final double range) {
        this.range = range;
    }
    
    public int getCount() {
        return this.count;
    }
    
    public void setCount(final int count) {
        this.count = count;
    }
    
    public double getRange() {
        return this.range;
    }
    
    public void effects(final Location loc) {
        new BukkitRunnable() {
            double r = 0.0;
            
            public void run() {
                if (this.r < WaterBlock.this.range) {
                    WaterBlock.this.effect(WaterBlock.this.location, this.r);
                    final Vector tmpVector = WaterBlock.this.player.getLocation().toVector().subtract(loc.toVector()).normalize();
                    WaterBlock.this.location.add(tmpVector.multiply(-1));
                    ++this.r;
                }
                else {
                    this.cancel();
                }
            }
        }.runTaskTimer((Plugin)ProjectKorra.plugin, 0L, 1L);
    }
    
    public void effect(final Location loc, final double r) {
        for (double theta = 0.0; theta < 360.0; theta += 20.0) {
            if (loc.getDirection().getX() == -0.0 && loc.getDirection().getY() == -0.0 && loc.getDirection().getZ() == 1.0) {
                final Vector tmpVector = this.player.getLocation().toVector().subtract(loc.toVector()).normalize();
                final Vector vector = GeneralMethods.getOrthogonalVector(tmpVector, theta, r);
                final Location location2 = loc.clone().add(vector);
                if (isAir(location2.getBlock().getType()) || (isWater(location2.getBlock()) && TempBlock.isTempBlock(location2.getBlock()))) {
                    new TempBlock(location2.getBlock(), Material.WATER).setRevertTime(1000L);
                    ParticleEffect.DRIP_WATER.display(location2, 1, 0.0, 0.0, 0.0, 0.05999999865889549);
                    ParticleEffect.WATER_DROP.display(location2, 1, 0.0, 0.0, 0.0, 0.05999999865889549);
                    for (final Entity entity : GeneralMethods.getEntitiesAroundPoint(this.location, 3.0)) {
                        if (entity instanceof LivingEntity && entity.getEntityId() != this.player.getEntityId()) {
                            GeneralMethods.setVelocity(entity, entity.getLocation().toVector().subtract(this.location.toVector()).normalize().multiply(this.knockback));
                        }
                    }
                }
            }
            else {
                final Vector vector2 = GeneralMethods.getOrthogonalVector(loc.getDirection(), theta, r);
                final Location location3 = loc.clone().add(vector2);
                if (isAir(location3.getBlock().getType()) || (isWater(location3.getBlock()) && TempBlock.isTempBlock(location3.getBlock()))) {
                    new TempBlock(location3.getBlock(), Material.WATER).setRevertTime(1000L);
                    ParticleEffect.DRIP_WATER.display(location3, 1, 0.0, 0.0, 0.0, 0.05999999865889549);
                    ParticleEffect.WATER_DROP.display(location3, 1, 0.0, 0.0, 0.0, 0.05999999865889549);
                    for (final Entity entity2 : GeneralMethods.getEntitiesAroundPoint(this.location, 3.0)) {
                        if (entity2 instanceof LivingEntity && entity2.getEntityId() != this.player.getEntityId()) {
                            GeneralMethods.setVelocity(entity2, entity2.getLocation().toVector().subtract(this.location.toVector()).normalize().multiply(this.knockback));
                        }
                    }
                }
            }
        }
    }
    
    public Object createNewComboInstance(final Player player) {
        return new WaterBlock(player);
    }
    
    public ArrayList<ComboManager.AbilityInformation> getCombination() {
        final ArrayList<ComboManager.AbilityInformation> combo = new ArrayList<ComboManager.AbilityInformation>();
        combo.add(new ComboManager.AbilityInformation("WaterManipulation", ClickType.SHIFT_DOWN));
        combo.add(new ComboManager.AbilityInformation("WaterManipulation", ClickType.LEFT_CLICK));
        return combo;
    }
    
    public String getDescription() {
        return "This ability makes you be able to re-manipulate the water within which the abilities that are called Torrent, WaterManipulation and Surge. Nevertheless, these abilities casted upon you need to be in sight of yours which means you can not block the Torrent coming behind of you. In addition to this, you could block WaterManipulation ability 3 times in-a-row.";
    }
    
    public String getInstructions() {
        return "PhaseChange (Hold Sneak) > PhaseChange (Left Click)";
    }
    
    public String getAuthor() {
        return "DreamerBoy & Hiro3";
    }
    
    public String getVersion() {
        return "1.2";
    }
    
    public double getWaterManipulationRange() {
        return this.manipRange;
    }
    
    public void setWaterManipulationRange(final double waterManipulationRange) {
        this.manipRange = waterManipulationRange;
    }
    
    public Location getLoc() {
        return this.location;
    }
    
    public boolean isCooldownOnOutOfTheSightView() {
        return this.cooldownOnOutOfTheSightView;
    }
    
    public void setLocation(final Location value) {
        this.location = value;
    }
    
    public void load() {
        this.WBL = (Listener)new WaterBlockListener();
        ProjectKorra.log.info(String.valueOf(this.getName()) + " " + this.getVersion() + " by " + this.getAuthor() + " enabled! ");
        ProjectKorra.plugin.getServer().getPluginManager().registerEvents(this.WBL, (Plugin)ProjectKorra.plugin);
        ConfigManager.getConfig().addDefault("ExtraAbilities.DreamerBoy.Water.WaterBlock.Cooldown.Cooldown", (Object)8000);
        ConfigManager.getConfig().addDefault("ExtraAbilities.DreamerBoy.Water.WaterBlock.Cooldown.CooldownOnOutOfTheSightView", (Object)true);
        ConfigManager.getConfig().addDefault("ExtraAbilities.DreamerBoy.Water.WaterBlock.Duration", (Object)0);
        ConfigManager.getConfig().addDefault("ExtraAbilities.DreamerBoy.Water.WaterBlock.Knockback", (Object)2);
        ConfigManager.getConfig().addDefault("ExtraAbilities.DreamerBoy.Water.WaterBlock.Radius", (Object)2);
        ConfigManager.getConfig().addDefault("ExtraAbilities.DreamerBoy.Water.WaterBlock.Range.WaterManipulation", (Object)4);
        ConfigManager.getConfig().addDefault("ExtraAbilities.DreamerBoy.Water.WaterBlock.Range.Others", (Object)8);
        ConfigManager.defaultConfig.save();
    }
    
    public void stop() {
        ProjectKorra.log.info(String.valueOf(this.getName()) + " " + this.getVersion() + " by " + this.getAuthor() + " disabled! ");
        HandlerList.unregisterAll(this.WBL);
        super.remove();
    }
}
