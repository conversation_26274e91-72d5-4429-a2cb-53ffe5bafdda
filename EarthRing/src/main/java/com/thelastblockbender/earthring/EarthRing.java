package com.thelastblockbender.earthring;

import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.permissions.PermissionDefault;
import org.bukkit.permissions.Permission;
import org.bukkit.plugin.Plugin;
import org.bukkit.event.Listener;
import com.projectkorra.projectkorra.ProjectKorra;
import java.io.File;
import java.util.List;
import org.bukkit.block.BlockFace;
import java.util.Random;
import java.util.ArrayList;
import org.bukkit.Color;
import com.projectkorra.projectkorra.earthbending.EarthArmor;
import org.bukkit.util.Vector;
import com.projectkorra.projectkorra.ability.Ability;
import com.projectkorra.projectkorra.util.DamageHandler;
import com.projectkorra.projectkorra.util.StatisticsMethods;

import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Entity;
import com.projectkorra.projectkorra.GeneralMethods;
import org.bukkit.block.Block;
import com.projectkorra.projectkorra.ability.CoreAbility;
import org.bukkit.entity.Player;
import org.bukkit.Location;
import com.projectkorra.projectkorra.util.TempBlock;
import org.bukkit.Material;
import com.projectkorra.projectkorra.configuration.Config;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.EarthAbility;

public class EarthRing extends EarthAbility implements AddonAbility
{
    public static Config config;
    private int hitpoints;
    private int grabRadius;
    private double damage;
    private double spDmg;
    private double speed;
    private double spin;
    private int maxRange;
    private int range;
    private double angle;
    private Material m;
    private TempBlock tb;
    private boolean spinning;
    private Location center;
    public Block source;
    public Material sourceType;
    private TempBlock spinningBlock;
    private long currentLevel;
    
    public EarthRing(final Player player) {
        super(player);
        this.range = 0;
        this.angle = 0.0;
        this.spinning = true;
        if (!this.bPlayer.canBend((CoreAbility)this)) {
            this.remove();
            return;
        }
        this.setFields();
        this.source = getRandomEarthBlock(player, player.getLocation(), this.grabRadius, true, true, true);
        this.sourceType = source.getType();
        if (source == null) {
            this.remove();
            return;
        }
        this.m = source.getType();
        this.tb = new TempBlock(source, Material.AIR);
        modify();
        this.start();
    }
    
    private void modify() {
		int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
		
		currentLevel = GeneralMethods.limitLevels(player, statLevel);
		
		this.maxRange = (int)(currentLevel + 8);
		this.grabRadius = (int)((currentLevel * 0.3) + 2);
	}
    
    
    public void setFields() {
        this.hitpoints = EarthRing.config.get().getInt("Abilities.EarthRing.HitPoints");
        this.grabRadius = EarthRing.config.get().getInt("Abilities.EarthRing.GrabRadius");
        this.damage = EarthRing.config.get().getDouble("Abilities.EarthRing.ShotDamage");
        this.spDmg = EarthRing.config.get().getDouble("Abilities.EarthRing.SpinDamage");
        this.speed = EarthRing.config.get().getDouble("Abilities.EarthRing.ShootSpeed");
        this.spin = EarthRing.config.get().getDouble("Abilities.EarthRing.SpinSpeed");
        this.maxRange = EarthRing.config.get().getInt("Abilities.EarthRing.MaxRange");
    }
    
    public long getCooldown() {
        return EarthRing.config.get().getLong("Abilities.EarthRing.Cooldown");
    }
    
    public Location getLocation() {
        return this.center;
    }
    
    public String getName() {
        return "EarthRing";
    }
    
    public boolean isHarmlessAbility() {
        return false;
    }
    
    public boolean isSneakAbility() {
        return true;
    }
    
    public void progress() {
        if (this.player == null) {
            this.remove();
            return;
        }
        if (!this.player.isOnline() || this.player.isDead()) {
            this.remove();
            return;
        }
        if (!this.player.isSneaking()) {
            this.remove();
            return;
        }
        if (this.spinning) {
            this.center = this.player.getEyeLocation().clone();
            final double x = Math.cos(this.angle);
            final double z = Math.sin(this.angle);
            this.center.add(x * 2.0, 0.0, z * 2.0);
            this.displayParticles();
            this.angle += 3.141592653589793 / this.spin;
            for (final Entity e : GeneralMethods.getEntitiesAroundPoint(this.center, 2.0)) {
                if (!(e instanceof LivingEntity)) {
                    continue;
                }
                if (e.getEntityId() == this.player.getEntityId()) {
                    continue;
                }
                DamageHandler.damageEntity(e, this.player, this.spDmg, (Ability)this);
                --this.hitpoints;
                this.remove();
                return;
            }
            if (this.hitpoints <= 0) {
                this.remove();
            }
        }
        else {
            final Vector direction = this.player.getLocation().getDirection().clone();
            this.center = this.center.add(direction.normalize().multiply(this.speed));
            ++this.range;
            if (this.range > this.maxRange) {
                this.remove();
                return;
            }
            this.displayParticles();
            for (final Entity e2 : GeneralMethods.getEntitiesAroundPoint(this.center, 1.5)) {
                if (!(e2 instanceof LivingEntity)) {
                    continue;
                }
                if (e2.getEntityId() == this.player.getEntityId()) {
                    continue;
                }
                DamageHandler.damageEntity(e2, this.player, this.damage, (Ability)this);
                this.remove();
                return;
            }
        }
    }
    
    public void displayParticles() {
        final Color color = Color.fromRGB(EarthArmor.getColor(this.m));
        final String hex = color.toString().substring(12, color.toString().length() - 1);
        final double x = Math.cos(this.angle);
        final double z = Math.sin(this.angle);
        //for (double x = -0.4; x < 0.5; x += 0.1) {
            //for (double y = -0.4; y < 0.5; y += 0.2) {
                //for (double z = -0.4; z < 0.5; z += 0.1) {
                    //if (Math.random() < 0.3) {
                        final Location clone = this.center.clone().add(x, 0, z);
                        spinningBlock = new TempBlock(clone.getBlock(), sourceType);
                        //GeneralMethods.displayColoredParticle(clone, hex, 0.0f, 0.0f, 0.0f);
                        //GeneralMethods.displayColoredParticle(hex, clone, 3, 0.0D, 0.0D, 0.0D);
                        //new TempBlock(clone.getBlock(), this.source.getType());	
                        //moveEarthBlock(this.animatedBlock, clone.getBlock());
                        spinningBlock.setRevertTime(50L);
                    // }
                }
            //}
        //}
    //}
    
    public static Block getRandomEarthBlock(final Player player, final Location location, final int radius, final boolean earth, final boolean sand, final boolean metal) {
        final List<Integer> checked = new ArrayList<Integer>();
        final List<Block> blocks = (List<Block>)GeneralMethods.getBlocksAroundPoint(location, (double)radius);
        for (int i = 0; i < blocks.size(); ++i) {
            int index;
            for (index = new Random().nextInt(blocks.size()); checked.contains(index); index = new Random().nextInt(blocks.size())) {}
            checked.add(index);
            final Block block = blocks.get(index);
            if (block != null) {
                if (block.getLocation().distance(location) >= 2.0) {
                    if (isTransparent(player, block.getRelative(BlockFace.UP))) {
                        if (isEarth(block) && earth) {
                            return block;
                        }
                        if (isSand(block) && sand) {
                            return block;
                        }
                        if (isMetal(block) && metal) {
                            return block;
                        }
                    }
                }
            }
        }
        return null;
    }
    
    public void setSpinning(final boolean spin) {
        this.spinning = spin;
    }
    
    public void remove() {
        super.remove();
        if (this.tb != null) {
            this.tb.setRevertTime(5000L);
            this.bPlayer.addCooldown((Ability)this);
        }
    }
    
    public String getAuthor() {
        return "Simp";
    }
    
    public String getVersion() {
        return "1.0.3";
    }
    
    public void load() {
        EarthRing.config = new Config(new File("Simplicitee.yml"));
        final FileConfiguration c = EarthRing.config.get();
        c.addDefault("Abilities.EarthRing.Cooldown", (Object)6000);
        c.addDefault("Abilities.EarthRing.HitPoints", (Object)5);
        c.addDefault("Abilities.EarthRing.GrabRadius", (Object)4);
        c.addDefault("Abilities.EarthRing.ShotDamage", (Object)2);
        c.addDefault("Abilities.EarthRing.SpinDamage", (Object)1);
        c.addDefault("Abilities.EarthRing.ShootSpeed", (Object)1.7);
        c.addDefault("Abilities.EarthRing.SpinSpeed", (Object)8);
        c.addDefault("Abilities.EarthRing.MaxRange", (Object)25);
        EarthRing.config.save();
        ProjectKorra.getCollisionInitializer().addLargeAbility((CoreAbility)this);
        ProjectKorra.getCollisionInitializer().addRemoveSpoutAbility((CoreAbility)this);
        ProjectKorra.plugin.getServer().getPluginManager().registerEvents((Listener)new EarthRingListener(), (Plugin)ProjectKorra.plugin);
        if (ProjectKorra.plugin.getServer().getPluginManager().getPermission("bending.ability.earthring") == null) {
            final Permission perm = new Permission("bending.ability.earthring");
            perm.setDefault(PermissionDefault.TRUE);
            ProjectKorra.plugin.getServer().getPluginManager().addPermission(perm);
        }
    }
    
    public void stop() {
    }
    
    public String getDescription() {
        return "Using advanced techniques, this ability allows the bender to create a small block of stones and earth moving around their body. This acts as a shield and can be turned offensive, and automatically sources the block it will use.";
    }
    
    public String getInstructions() {
        return "Sneak while on or near earthbendable blocks to make earth circle you, click to shoot the earth in the direction you are looking.";
    }
}