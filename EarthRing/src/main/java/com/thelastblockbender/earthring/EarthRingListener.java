package com.thelastblockbender.earthring;

import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerAnimationEvent;
import org.bukkit.event.player.PlayerToggleSneakEvent;

import com.projectkorra.projectkorra.ability.CoreAbility;

public class EarthRing<PERSON><PERSON>ener implements Listener
{
    @EventHandler
    public void onSneak(final PlayerToggleSneakEvent event) {
        if (event.isCancelled()) {
            return;
        }
        if (event.getPlayer().isSneaking()) {
            return;
        }
        new EarthRing(event.getPlayer());
    }
    
    @EventHandler
    public void onSwing(final PlayerAnimationEvent event) {
        if (event.isCancelled()) {
            return;
        }
        if (CoreAbility.hasAbility(event.getPlayer(), (Class)EarthRing.class)) {
            final EarthRing abil = (EarthRing)CoreAbility.getAbility(event.getPlayer(), (Class)EarthRing.class);
            abil.setSpinning(false);
        }
    }
}